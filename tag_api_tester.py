#!/usr/bin/env python3
"""
TAG Global API 端点测试工具
测试所有可能的 API 端点并收集结果
"""

import requests
import json
import time
from urllib.parse import urlparse
import csv
from datetime import datetime

class TagApiTester:
    def __init__(self, token):
        self.token = token
        self.base_url = "https://global.tagonline.asia"
        self.results = []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'TAG-API-Tester/1.0',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        })
    
    def test_endpoint(self, url, category="Unknown"):
        """测试单个端点"""
        try:
            print(f"Testing: {url}")
            response = self.session.get(url, timeout=10)
            
            result = {
                'category': category,
                'url': url,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response_size': len(response.content),
                'content_type': response.headers.get('content-type', ''),
                'timestamp': datetime.now().isoformat()
            }
            
            # 尝试解析 JSON 响应
            try:
                json_data = response.json()
                result['has_json'] = True
                result['json_keys'] = list(json_data.keys()) if isinstance(json_data, dict) else []
                result['response_preview'] = str(json_data)[:200] + "..." if len(str(json_data)) > 200 else str(json_data)
            except:
                result['has_json'] = False
                result['response_preview'] = response.text[:200] + "..." if len(response.text) > 200 else response.text
            
            self.results.append(result)
            
            # 打印结果摘要
            status_emoji = "✅" if result['success'] else "❌"
            print(f"  {status_emoji} {response.status_code} - {result['content_type']}")
            if result['success'] and result['has_json']:
                print(f"  📊 JSON Keys: {result['json_keys']}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            result = {
                'category': category,
                'url': url,
                'status_code': 'ERROR',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.results.append(result)
            print(f"  ❌ ERROR: {e}")
            return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试 TAG Global API 端点...")
        print("=" * 60)
        
        # 定义所有端点
        endpoints = {
            "用户信息类": [
                "/api/v1/client/info",
                "/api/v1/user/info",
                "/api/v1/user/status",
                "/api/v1/user/detail",
                "/api/v1/account/info",
                "/api/v1/member/info",
                "/api/v1/profile"
            ],
            "流量查询类": [
                "/api/v1/user/traffic",
                "/api/v1/client/traffic",
                "/api/v1/user/usage",
                "/api/v1/client/usage",
                "/api/v1/user/stats",
                "/api/v1/traffic/info",
                "/api/v1/traffic/usage",
                "/api/v1/bandwidth",
                "/api/v1/data/usage"
            ],
            "套餐信息类": [
                "/api/v1/user/package",
                "/api/v1/user/plan",
                "/api/v1/client/plan",
                "/api/v1/subscription/info",
                "/api/v1/subscription/detail",
                "/api/v1/plan/info",
                "/api/v1/package/detail"
            ],
            "统计和日志类": [
                "/api/v1/user/log",
                "/api/v1/user/statistics",
                "/api/v1/traffic/statistics",
                "/api/v1/stats",
                "/api/v1/analytics",
                "/api/v1/history"
            ],
            "服务状态类": [
                "/api/v1/server/status",
                "/api/v1/node/status",
                "/api/v1/service/status"
            ],
            "不同版本": [
                "/api/v2/user/info",
                "/api/v2/user/traffic",
                "/api/user/info",
                "/api/client/info",
                "/api/user"
            ],
            "其他端点": [
                "/api/v1/dashboard",
                "/api/v1/summary",
                "/api/v1/overview",
                "/api/v1/account",
                "/api/v1/billing",
                "/api/v1/invoice"
            ]
        }
        
        # 测试标准 token 参数
        for category, paths in endpoints.items():
            print(f"\n📂 测试类别: {category}")
            print("-" * 40)
            for path in paths:
                url = f"{self.base_url}{path}?token={self.token}"
                self.test_endpoint(url, category)
                time.sleep(0.5)  # 避免请求过于频繁
        
        # 测试不同参数名
        print(f"\n📂 测试类别: 不同参数名")
        print("-" * 40)
        param_variants = ['access_token', 'key', 'uuid', 'auth', 'api_key']
        for param in param_variants:
            url = f"{self.base_url}/api/v1/user/info?{param}={self.token}"
            self.test_endpoint(url, "不同参数名")
            time.sleep(0.5)
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        successful_endpoints = [r for r in self.results if r['success']]
        failed_endpoints = [r for r in self.results if not r['success']]
        
        print(f"✅ 成功: {len(successful_endpoints)} 个端点")
        print(f"❌ 失败: {len(failed_endpoints)} 个端点")
        print(f"📊 总计: {len(self.results)} 个端点")
        
        if successful_endpoints:
            print(f"\n🎉 成功的端点:")
            for result in successful_endpoints:
                print(f"  ✅ {result['url']}")
                if result.get('json_keys'):
                    print(f"     📊 数据字段: {', '.join(result['json_keys'])}")
        
        # 保存详细结果到文件
        self.save_results()
    
    def save_results(self):
        """保存结果到文件"""
        # 保存 JSON 格式
        with open('tag_api_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 保存 CSV 格式
        if self.results:
            with open('tag_api_test_results.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.results[0].keys())
                writer.writeheader()
                writer.writerows(self.results)
        
        print(f"\n💾 结果已保存到:")
        print(f"  📄 tag_api_test_results.json")
        print(f"  📊 tag_api_test_results.csv")

def main():
    # 您的 token
    token = "9fe0baab7e2b181e70e93b39aecdd3cb"
    
    # 创建测试器并运行
    tester = TagApiTester(token)
    tester.run_all_tests()
    tester.generate_report()

if __name__ == "__main__":
    main()
