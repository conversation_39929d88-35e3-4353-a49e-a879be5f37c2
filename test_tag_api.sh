#!/bin/bash

# TAG Global API 端点测试脚本
# Token: 9fe0baab7e2b181e70e93b39aecdd3cb

TOKEN="9fe0baab7e2b181e70e93b39aecdd3cb"
BASE_URL="https://global.tagonline.asia"
RESULTS_FILE="api_test_results.txt"

# 清空结果文件
> "$RESULTS_FILE"

echo "🚀 开始测试 TAG Global API 端点..."
echo "测试时间: $(date)" | tee -a "$RESULTS_FILE"
echo "========================================" | tee -a "$RESULTS_FILE"

# 测试函数
test_endpoint() {
    local url="$1"
    local category="$2"
    
    echo "Testing: $url"
    
    # 使用 curl 测试端点
    response=$(curl -s -w "HTTPSTATUS:%{http_code};SIZE:%{size_download};TYPE:%{content_type}" \
                   -H "Accept: application/json" \
                   -H "User-Agent: TAG-API-Tester/1.0" \
                   --connect-timeout 10 \
                   --max-time 30 \
                   "$url" 2>/dev/null)
    
    # 提取状态信息
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    size=$(echo "$response" | grep -o "SIZE:[0-9]*" | cut -d: -f2)
    content_type=$(echo "$response" | grep -o "TYPE:[^;]*" | cut -d: -f2)
    body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*;SIZE:[0-9]*;TYPE:[^;]*$//')
    
    # 判断结果
    if [ "$http_code" = "200" ]; then
        status="✅ SUCCESS"
        echo "  ✅ $http_code - $content_type (${size} bytes)"
        # 如果是 JSON，尝试格式化显示
        if [[ "$content_type" == *"json"* ]] && command -v jq >/dev/null 2>&1; then
            echo "  📊 JSON Preview:"
            echo "$body" | jq -C . 2>/dev/null | head -5 | sed 's/^/    /'
        fi
    else
        status="❌ FAILED"
        echo "  ❌ $http_code"
    fi
    
    # 记录到文件
    echo "[$category] $url - $status ($http_code)" >> "$RESULTS_FILE"
    
    # 避免请求过于频繁
    sleep 0.5
}

echo ""
echo "📂 用户信息类 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/client/info?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/user/info?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/user/status?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/user/detail?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/account/info?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/member/info?token=$TOKEN" "用户信息"
test_endpoint "$BASE_URL/api/v1/profile?token=$TOKEN" "用户信息"

echo ""
echo "📂 流量查询类 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/user/traffic?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/client/traffic?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/user/usage?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/client/usage?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/user/stats?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/traffic/info?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/traffic/usage?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/bandwidth?token=$TOKEN" "流量查询"
test_endpoint "$BASE_URL/api/v1/data/usage?token=$TOKEN" "流量查询"

echo ""
echo "📂 套餐信息类 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/user/package?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/user/plan?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/client/plan?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/subscription/info?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/subscription/detail?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/plan/info?token=$TOKEN" "套餐信息"
test_endpoint "$BASE_URL/api/v1/package/detail?token=$TOKEN" "套餐信息"

echo ""
echo "📂 统计和日志类 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/user/log?token=$TOKEN" "统计日志"
test_endpoint "$BASE_URL/api/v1/user/statistics?token=$TOKEN" "统计日志"
test_endpoint "$BASE_URL/api/v1/traffic/statistics?token=$TOKEN" "统计日志"
test_endpoint "$BASE_URL/api/v1/stats?token=$TOKEN" "统计日志"
test_endpoint "$BASE_URL/api/v1/analytics?token=$TOKEN" "统计日志"
test_endpoint "$BASE_URL/api/v1/history?token=$TOKEN" "统计日志"

echo ""
echo "📂 服务状态类 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/server/status?token=$TOKEN" "服务状态"
test_endpoint "$BASE_URL/api/v1/node/status?token=$TOKEN" "服务状态"
test_endpoint "$BASE_URL/api/v1/service/status?token=$TOKEN" "服务状态"

echo ""
echo "📂 不同版本的 API"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v2/user/info?token=$TOKEN" "不同版本"
test_endpoint "$BASE_URL/api/v2/user/traffic?token=$TOKEN" "不同版本"
test_endpoint "$BASE_URL/api/user/info?token=$TOKEN" "不同版本"
test_endpoint "$BASE_URL/api/client/info?token=$TOKEN" "不同版本"
test_endpoint "$BASE_URL/api/user?token=$TOKEN" "不同版本"

echo ""
echo "📂 不同参数名测试"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/user/info?access_token=$TOKEN" "不同参数"
test_endpoint "$BASE_URL/api/v1/user/info?key=$TOKEN" "不同参数"
test_endpoint "$BASE_URL/api/v1/user/info?uuid=$TOKEN" "不同参数"
test_endpoint "$BASE_URL/api/v1/user/info?auth=$TOKEN" "不同参数"
test_endpoint "$BASE_URL/api/v1/user/info?api_key=$TOKEN" "不同参数"

echo ""
echo "📂 其他可能的端点"
echo "----------------------------------------"
test_endpoint "$BASE_URL/api/v1/dashboard?token=$TOKEN" "其他端点"
test_endpoint "$BASE_URL/api/v1/summary?token=$TOKEN" "其他端点"
test_endpoint "$BASE_URL/api/v1/overview?token=$TOKEN" "其他端点"
test_endpoint "$BASE_URL/api/v1/account?token=$TOKEN" "其他端点"
test_endpoint "$BASE_URL/api/v1/billing?token=$TOKEN" "其他端点"
test_endpoint "$BASE_URL/api/v1/invoice?token=$TOKEN" "其他端点"

echo ""
echo "========================================" | tee -a "$RESULTS_FILE"
echo "📊 测试完成！结果汇总:" | tee -a "$RESULTS_FILE"
echo "========================================" | tee -a "$RESULTS_FILE"

# 统计结果
success_count=$(grep -c "SUCCESS" "$RESULTS_FILE")
failed_count=$(grep -c "FAILED" "$RESULTS_FILE")
total_count=$((success_count + failed_count))

echo "✅ 成功: $success_count 个端点" | tee -a "$RESULTS_FILE"
echo "❌ 失败: $failed_count 个端点" | tee -a "$RESULTS_FILE"
echo "📊 总计: $total_count 个端点" | tee -a "$RESULTS_FILE"

echo ""
echo "💾 详细结果已保存到: $RESULTS_FILE"

if [ $success_count -gt 0 ]; then
    echo ""
    echo "🎉 成功的端点列表:"
    grep "SUCCESS" "$RESULTS_FILE" | sed 's/^/  /'
fi
