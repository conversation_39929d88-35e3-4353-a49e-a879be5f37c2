# TAG Global API 端点测试指南

## 概述
这个项目包含了测试 TAG Global API 所有可能端点的工具和脚本。

**Token**: `9fe0baab7e2b181e70e93b39aecdd3cb`

## 文件说明

### 1. `tag_api_tester.py` - Python 测试脚本
功能最全面的测试工具，包含：
- 系统化测试所有端点
- JSON 响应解析和预览
- 详细的错误处理
- 生成 JSON 和 CSV 格式的测试报告
- 彩色输出和进度显示

**使用方法：**
```bash
python3 tag_api_tester.py
```

**依赖：**
```bash
pip3 install requests
```

### 2. `test_tag_api.sh` - Bash 测试脚本
轻量级的 shell 脚本，适合快速测试：
- 使用 curl 进行 HTTP 请求
- 实时显示测试结果
- 生成简单的文本报告
- 无需额外依赖

**使用方法：**
```bash
./test_tag_api.sh
```

## 测试的 API 端点类别

### 📂 用户信息类 API
- `/api/v1/client/info`
- `/api/v1/user/info`
- `/api/v1/user/status`
- `/api/v1/user/detail`
- `/api/v1/account/info`
- `/api/v1/member/info`
- `/api/v1/profile`

### 📂 流量查询类 API
- `/api/v1/user/traffic`
- `/api/v1/client/traffic`
- `/api/v1/user/usage`
- `/api/v1/client/usage`
- `/api/v1/user/stats`
- `/api/v1/traffic/info`
- `/api/v1/traffic/usage`
- `/api/v1/bandwidth`
- `/api/v1/data/usage`

### 📂 套餐信息类 API
- `/api/v1/user/package`
- `/api/v1/user/plan`
- `/api/v1/client/plan`
- `/api/v1/subscription/info`
- `/api/v1/subscription/detail`
- `/api/v1/plan/info`
- `/api/v1/package/detail`

### 📂 统计和日志类 API
- `/api/v1/user/log`
- `/api/v1/user/statistics`
- `/api/v1/traffic/statistics`
- `/api/v1/stats`
- `/api/v1/analytics`
- `/api/v1/history`

### 📂 服务状态类 API
- `/api/v1/server/status`
- `/api/v1/node/status`
- `/api/v1/service/status`

### 📂 不同版本的 API
- `/api/v2/user/info`
- `/api/v2/user/traffic`
- `/api/user/info`
- `/api/client/info`
- `/api/user`

### 📂 不同参数名测试
测试使用不同参数名传递 token：
- `access_token`
- `key`
- `uuid`
- `auth`
- `api_key`

### 📂 其他可能的端点
- `/api/v1/dashboard`
- `/api/v1/summary`
- `/api/v1/overview`
- `/api/v1/account`
- `/api/v1/billing`
- `/api/v1/invoice`

## 手动测试示例

### 使用 curl 命令
```bash
# 测试用户信息
curl -X GET "https://global.tagonline.asia/api/v1/user/info?token=9fe0baab7e2b181e70e93b39aecdd3cb" \
     -H "Accept: application/json" \
     -H "User-Agent: TAG-API-Tester/1.0"

# 测试流量信息
curl -X GET "https://global.tagonline.asia/api/v1/user/traffic?token=9fe0baab7e2b181e70e93b39aecdd3cb" \
     -H "Accept: application/json"

# 测试套餐信息
curl -X GET "https://global.tagonline.asia/api/v1/user/plan?token=9fe0baab7e2b181e70e93b39aecdd3cb" \
     -H "Accept: application/json"
```

### 使用 Python requests
```python
import requests

token = "9fe0baab7e2b181e70e93b39aecdd3cb"
base_url = "https://global.tagonline.asia"

# 测试用户信息
response = requests.get(f"{base_url}/api/v1/user/info?token={token}")
print(f"Status: {response.status_code}")
if response.status_code == 200:
    print(f"Data: {response.json()}")
```

## 输出文件

### Python 脚本输出
- `tag_api_test_results.json` - 详细的 JSON 格式结果
- `tag_api_test_results.csv` - CSV 格式结果，便于 Excel 分析

### Bash 脚本输出
- `api_test_results.txt` - 简单的文本格式结果

## 状态码说明

- **200** ✅ - 成功，API 端点存在且返回数据
- **404** ❌ - 端点不存在
- **401/403** ❌ - 认证失败或无权限
- **500** ❌ - 服务器内部错误
- **ERROR** ❌ - 网络连接错误

## 注意事项

1. **安全性**: 这是您的个人 token，请勿分享给他人
2. **频率限制**: 避免过于频繁的请求，可能会被限制
3. **网络环境**: 确保网络连接正常，可以访问 global.tagonline.asia
4. **结果解读**: 
   - 成功的端点可以用于获取实际数据
   - 失败的端点可能不存在或需要不同的认证方式

## 快速开始

1. **运行 Python 脚本（推荐）**:
   ```bash
   python3 tag_api_tester.py
   ```

2. **或运行 Bash 脚本**:
   ```bash
   ./test_tag_api.sh
   ```

3. **查看结果文件**:
   ```bash
   # Python 脚本结果
   cat tag_api_test_results.json
   
   # Bash 脚本结果
   cat api_test_results.txt
   ```

测试完成后，您将获得所有可用 API 端点的完整列表！
