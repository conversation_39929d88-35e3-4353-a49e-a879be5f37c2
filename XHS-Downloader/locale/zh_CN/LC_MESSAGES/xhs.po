# Chinese translations for XHS-Downloader package
# Copyright (C) 2024 THE XHS-Downloader'S COPYRIGHT HOLDER
# This file is distributed under the same license as the XHS-Downloader package.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: XHS-Downloader 2.4\n"
"Report-Msgid-Bugs-To: <<EMAIL>>\n"
"POT-Creation-Date: 2024-12-28 16:54+0800\n"
"PO-Revision-Date: 2024-12-22 14:14+0800\n"
"Last-Translator: <<EMAIL>>\n"
"Language-Team: Chinese (simplified)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:157
#, python-brace-format
msgid "作品 {0} 存在下载记录，跳过下载"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:170
msgid "提取作品文件下载地址失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:196
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:214
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:444
msgid "提取小红书作品链接失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:199
#, python-brace-format
msgid "共 {0} 个小红书作品待处理..."
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:244
#, python-brace-format
msgid "作品 {0} 存在下载记录，跳过处理"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:247
#, python-brace-format
msgid "开始处理作品：{0}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:251
#, python-brace-format
msgid "{0} 获取数据失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:256
#, python-brace-format
msgid "{0} 提取数据失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:258
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:75
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:13
msgid "视频"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:260
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:82
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:13
msgid "图文"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:265
#, python-brace-format
msgid "作品处理完成：{0}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:326
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:54
msgid ""
"程序会自动读取并提取剪贴板中的小红书作品链接，并自动下载链接对应的作品文件，"
"如需关闭，请点击关闭按钮，或者向剪贴板写入 “close” 文本！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:456
msgid "获取小红书作品数据成功"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\app.py:458
msgid "获取小红书作品数据失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:118
msgid "视频作品下载功能已关闭，跳过下载"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:134
msgid "图文作品下载功能已关闭，跳过下载"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:162
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:170
#, python-brace-format
msgid "{0} 文件已存在，跳过下载"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:208
#, python-brace-format
msgid "文件 {0} 缓存异常，重新下载"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:231
#, python-brace-format
msgid "文件 {0} 下载成功"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:238
#, python-brace-format
msgid "网络异常，{0} 下载失败，错误信息: {1}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\download.py:311
#, python-brace-format
msgid "文件 {0} 格式判断失败，错误信息：{1}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:50
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:58
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\explore.py:63
msgid "未知"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\application\request.py:44
#, python-brace-format
msgid "网络异常，{0} 请求失败: {1}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:118
msgid "小红书作品链接"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:119
msgid "下载指定序号的图片文件，仅对图文作品生效；多个序号输入示例：\"1 3 5 7\""
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:120
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:33
msgid "作品数据 / 文件保存根路径"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:121
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:36
msgid "作品文件储存文件夹名称"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:122
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:38
msgid "作品文件名称格式"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:126
msgid "小红书网页版 Cookie，无需登录"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:127
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:46
msgid "网络代理"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:128
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:48
msgid "请求数据超时限制，单位：秒"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:129
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:50
msgid "下载文件时，每次从服务器获取的数据块大小，单位：字节"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:130
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:52
msgid "请求数据失败时，重试的最大次数"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:131
msgid "是否记录作品数据至文件"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:132
msgid "图文作品文件下载格式，支持：PNG、WEBP"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:133
msgid "动态图片下载开关"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:134
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:64
msgid "作品下载记录开关"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:135
msgid "是否将每个作品的文件储存至单独的文件夹"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:136
msgid "设置程序语言，目前支持：zh_CN、en_US"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:137
msgid "读取指定配置文件"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:139
#, python-brace-format
msgid "从指定的浏览器读取小红书网页版 Cookie，支持：{0}; 输入浏览器名称或序号"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:145
msgid "是否更新配置文件"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:146
msgid "查看详细参数说明"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\CLI\main.py:147
msgid "查看 XHS-Downloader 版本"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:45
#, python-brace-format
msgid ""
"读取指定浏览器的 Cookie 并写入配置文件\n"
"Windows 系统需要以管理员身份运行程序才能读取 Chromium、Chrome、Edge 浏览器 "
"Cookie！\n"
"{options}\n"
"请输入浏览器名称或序号："
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:49
msgid "未选择浏览器！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:55
msgid "浏览器名称或序号输入错误！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:61
msgid "获取 Cookie 失败，未找到 Cookie 数据！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\browser.py:99
msgid "从浏览器读取 Cookie 功能不支持当前平台！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\expansion\cleaner.py:45
msgid "不受支持的操作系统类型，可能无法正常去除非法字符！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:204
#, python-brace-format
msgid "代理 {0} 测试成功"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:208
#, python-brace-format
msgid "代理 {0} 测试超时"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\module\manager.py:216
#, python-brace-format
msgid "代理 {0} 测试失败：{1}"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:26
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:37
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:26
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:22
msgid "退出程序"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:30
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:38
msgid "检查更新"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:34
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:28
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:23
msgid "返回首页"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:44
msgid "如果 XHS-Downloader 对您有帮助，请考虑为它点个 Star，感谢您的支持！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:49
msgid "Discord 社区"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:51
msgid "邀请链接："
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:53
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:60
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:66
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:65
msgid "点击访问"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\about.py:55
msgid "作者的其他开源项目"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\app.py:62
msgid ""
"配置文件 settings.json 缺少必要的参数，请删除该文件，然后重新运行程序，自动生"
"成默认配置文件！"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:39
#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:97
msgid "程序设置"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:40
msgid "下载记录"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:41
msgid "开启监听"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:42
msgid "关于项目"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:56
msgid "开源协议: "
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:61
msgid "项目地址: "
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:69
msgid "请输入小红书图文/视频作品链接"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:72
msgid "多个链接之间使用空格分隔"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:74
msgid "下载无水印作品文件"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:75
msgid "读取剪贴板"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:76
msgid "清空输入框"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:88
msgid "免责声明\n"
msgstr ""
"关于 XHS-Downloader 的 免责声明：\n"
"\n"
"1.使用者对本项目的使用由使用者自行决定，并自行承担风险。作者对使用者使用本项"
"目所产生的任何损失、责任、或风险概不负责。\n"
"2.本项目的作者提供的代码和功能是基于现有知识和技术的开发成果。作者尽力确保代"
"码的正确性和安全性，但不保证代码完全没有错误或缺陷。\n"
"3.使用者在使用本项目时必须严格遵守 GNU General Public License v3.0 的要求，并"
"在适当的地方注明使用了 GNU General Public License v3.0 的代码。\n"
"4.使用者在任何情况下均不得将本项目的作者、贡献者或其他相关方与使用者的使用行"
"为联系起来，或要求其对使用者使用本项目所产生的任何损失或损害负责。\n"
"5.使用者在使用本项目的代码和功能时，必须自行研究相关法律法规，并确保其使用行"
"为合法合规。任何因违反法律法规而导致的法律责任和风险，均由使用者自行承担。\n"
"6.本项目的作者不会提供 XHS-Downloader 项目的付费版本，也不会提供与 XHS-"
"Downloader 项目相关的任何商业服务。\n"
"7.基于本项目进行的任何二次开发、修改或编译的程序与原创作者无关，原创作者不承"
"担与二次开发行为或其结果相关的任何责任，使用者应自行对因二次开发可能带来的各"
"种情况负全部责任。\n"
"\n"
"在使用本项目的代码和功能之前，请您认真考虑并接受以上免责声明。如果您对上述声"
"明有任何疑问或不同意，请不要使用本项目的代码和功能。如果您使用了本项目的代码"
"和功能，则视为您已完全理解并接受上述免责声明，并自愿承担使用本项目的一切风险"
"和后果。\n"

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:103
msgid "未输入任何小红书作品链接"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\index.py:126
msgid "下载小红书作品文件失败"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\loading.py:18
msgid "程序处理中..."
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:27
msgid "关闭监听"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:36
msgid "已启动监听剪贴板模式"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\monitor.py:38
msgid "退出监听剪贴板模式"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:23
msgid "请输入待删除的小红书作品链接或作品 ID"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:24
msgid ""
"支持输入作品 ID 或包含作品 ID 的作品链接，多个链接或 ID 之间使用空格分隔"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\record.py:27
msgid "删除指定作品 ID"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:34
msgid "程序根路径"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:42
msgid "内置 Chrome User Agent"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:44
msgid "小红书网页版 Cookie"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:47
msgid "不使用代理"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:56
msgid "记录作品详细数据"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:57
msgid "作品文件夹归档模式"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:58
msgid "视频作品下载开关"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:59
msgid "图文作品下载开关"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:63
msgid "动图文件下载开关"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:67
msgid "图片下载格式"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:68
msgid "程序语言"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:85
msgid "保存配置"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:86
msgid "放弃更改"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:93
msgid "小红书网页版 Cookie，无需登录，参数已设置"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\setting.py:94
msgid "小红书网页版 Cookie，无需登录，参数未设置"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:28
msgid "正在检查新版本，请稍等..."
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:48
msgid "当前已是最新开发版"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:52
msgid "当前已是最新正式版"
msgstr ""

#: C:\Users\<USER>\PycharmProjects\XHS-Downloader\source\TUI\update.py:57
msgid "检测新版本失败"
msgstr ""
