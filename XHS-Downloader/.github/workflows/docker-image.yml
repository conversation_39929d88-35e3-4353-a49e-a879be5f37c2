name: 自动构建并发布 Docker 镜像

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  publish-docker:
    runs-on: ubuntu-latest

    steps:
      - name: 拉取源码
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: 设置 QEMU
        uses: docker/setup-qemu-action@v3

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到 DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 构建和推送 Docker hub
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xhs-downloader:${{ github.event.release.tag_name }}
