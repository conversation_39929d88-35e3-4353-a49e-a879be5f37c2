name: 发布构建可执行文件

on:
  release:
    types: [ created ]

permissions:
  contents: write
  discussions: write

jobs:
  build:
    name: 构建于 ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ macos-latest, windows-latest, macos-13 ]

    steps:
      - name: 签出存储库
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: 安装依赖项
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pyinstaller

      - name: 构建 Win 可执行文件
        if: runner.os == 'Windows'
        run: |
          pyinstaller --icon=./static/XHS-Downloader.ico --add-data "static:static" --add-data "locale:locale" --collect-all emoji main.py
        shell: pwsh

      - name: 构建 Mac 可执行文件
        if: runner.os == 'macOS'
        run: |
          pyinstaller --icon=./static/XHS-Downloader.icns --add-data "static:static" --add-data "locale:locale" --collect-all emoji main.py

      - name: 创建压缩包
        run: |
          7z a "XHS-Downloader_V${{ github.event.release.tag_name }}_${{ runner.os }}_${{ runner.arch }}.zip" ./dist/main/*
        shell: bash

      - name: 上传文件到 release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            ./XHS-Downloader_V*.zip
          name: XHS-Downloader V${{ github.event.release.tag_name }}
          body_path: ./static/Release_Notes.md
          draft: ${{ github.event.release.draft }}
          prerelease: ${{ github.event.release.prerelease }}
