name: 手动构建可执行文件

on:
  workflow_dispatch:

jobs:
  build:
    name: 构建于 ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ macos-latest, windows-latest, macos-13 ]

    steps:
      - name: 签出存储库
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: 安装依赖项
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pyinstaller

      - name: 构建 Win 可执行文件
        if: runner.os == 'Windows'
        run: |
          pyinstaller --icon=./static/XHS-Downloader.ico --add-data "static:static" --add-data "locale:locale" --collect-all emoji main.py
        shell: pwsh

      - name: 构建 Mac 可执行文件
        if: runner.os == 'macOS'
        run: |
          pyinstaller --icon=./static/XHS-Downloader.icns --add-data "static:static" --add-data "locale:locale" --collect-all emoji main.py

      - name: 上传文件
        uses: actions/upload-artifact@v4
        with:
          name: XHS-Downloader_${{ runner.os }}_${{ runner.arch }}
          path: dist/main/
